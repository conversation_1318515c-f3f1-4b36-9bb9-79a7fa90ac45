# Task ID: 7
# Title: Build CLI Client with Interactive Interface
# Status: pending
# Dependencies: 1, 2, 3, 5
# Priority: medium
# Description: Create a full-featured command-line interface client using Node.js readline module for customer and provider operations with state management
# Details:
Implement interactive CLI using readline module with menu navigation. Create state management for user sessions and cart data. Build API client for server communication. Implement command parsing and validation. Create formatted output for orders and menus. Example implementation:

```javascript
// clients/cli/index.js
const readline = require('readline');
const https = require('https');

class CLIClient {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.session = null;
    this.cart = [];
  }
  
  async start() {
    console.log('Welcome to Pizza Delivery CLI!');
    await this.showMainMenu();
  }
  
  async showMainMenu() {
    const choices = this.session 
      ? ['1. Browse Menu', '2. View Cart', '3. Place Order', '4. Order History', '5. Logout']
      : ['1. Login', '2. Register', '3. Exit'];
    
    console.log('\nMain Menu:');
    choices.forEach(choice => console.log(choice));
    
    const answer = await this.prompt('Select option: ');
    await this.handleMenuChoice(answer);
  }
  
  async browseMenu() {
    const menu = await this.apiCall('GET', '/api/menu');
    console.log('\nAvailable Pizzas:');
    menu.items.forEach((pizza, index) => {
      console.log(`${index + 1}. ${pizza.name} - $${pizza.price}`);
      console.log(`   ${pizza.description}`);
    });
  }
  
  prompt(question) {
    return new Promise(resolve => {
      this.rl.question(question, resolve);
    });
  }
}
```

# Test Strategy:
Test menu navigation flow. Verify session persistence across commands. Test cart operations and order placement. Validate input handling and error messages. Test API communication error handling.
