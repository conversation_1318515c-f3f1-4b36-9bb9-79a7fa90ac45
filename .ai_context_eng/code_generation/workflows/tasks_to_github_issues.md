---
description: Comprehensive guide for pairing project tasks with Github issues.
---
# Pairing Tasks to GitHub Issues

This document provides a detailed reference for registering Github issues, Labels and Milestones from the project Tasks. The goal is to pair each project task  with an issue on Github, helping to track the project progress automatically and leveraging the advantages of being integrated with Github.

## Workflow Requirements

The workflow for pairing tasks with Github issues requires tools from:
*   Desktop Commander MCP Server (https://desktopcommander.app/) or Filesystem MCP Server(https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem).
*   Github MCP Server (https://github.com/github/github-mcp-server)
*   Git MCP Server (https://github.com/modelcontextprotocol/servers/tree/main/src/git)
*   Git CLI (https://cli.github.com/)
*   Git initialized on the directory (to be located in a git project directory).

## Register Issues in Github

### 1. Create a Github issue for each task.

*   List all project's tasks (with subtasks if any) and create an issue per task. Each task must map to one GitHub issue.
*   Issue naming should follow the next format: <Task Number>: <Task Name>
*   Do not check for issues existence, just create the issue.
*   Task description should be included in the issue body.
*   Subtasks should render as checklist items in the issue body.
*   Assign the corresponding Labels and the Milestone to the issue when request its creation. Before call to register the issue, show the issue info to the user and ask for review if required(specially for the Milestone number).
*   Use tools provided by Github MCP server, and also use tools provided by Context7 MCP  server in case you need to access up to date documentation to verify how to do something.
*   Once an issue is registered on Github, update the corresponding Project task with adding reference to it corresponding issue. Find next the steps to follow for this.

### 2. Update the Project task with reference to the corresponding Github issue.
*   Once an issue is registered on Github, take the issue number and update the corresponding Project task by adding a new field called "github_issue" with the value of the issue number.
* The Github issue's reference will be used to "close" the issue (on its corresponding Pull Request) once the task is completed.

### 2.1 Illustrative examples  of how the tasks should look like after updating them with the reference to the corresponding Github issue.

```markdown
# Task ID: 1
# Title: Setup Project Foundation and HTTP/HTTPS Server
# Status: pending
# Dependencies: []
# Priority: high
# Description: Initialize project structure and implement a custom HTTP/HTTPS server using only Node.js built-in modules with SSL support, custom routing, and middleware pipeline
# Details:
# Test Strategy: Test server startup, SSL, custom routing, and middleware functionality
# github_issue: 1

....<the rest of the tasks>
```
