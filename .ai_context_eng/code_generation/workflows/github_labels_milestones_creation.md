---
description: Comprehensive guide for generating and registering Labels and Milestones in Github given a PRD and a set of tasks.
---
# Github's Labels and Milestones creation from PRD and tasks

This document provides a detailed reference for registering Github Labels and Milestones from the project Tasks. The goal is to 
generate the required Labels and Milestones from the PRD and the tasks, and then register them in Github automatically, to be used for tracking the project progress on Github.

## Workflow Requirements

The workflow for generating Labels and Milestones from PRD and Tasks, and registering on Github requires tools from:
*   Desktop Commander MCP Server (https://desktopcommander.app/) or Filesystem MCP Server(https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem).
*   Github MCP Server (https://github.com/github/github-mcp-server)
*   Git MCP Server (https://github.com/modelcontextprotocol/servers/tree/main/src/git)
*   Git CLI (https://cli.github.com/)
*   Git initialized on the directory (to be located in a git project directory).

## Register labels and Milestones in Github

### 1. Remove existing Labels and Milestones from Github.
*   Ask the user if he wants to remove all existing Labels from the current Github repository before generating the new ones.
*   Ask the user if he wants to remove all existing Milestones from the current Github repository before generating the new ones.
*   For each case, if the user wants to remove the existing Labels/Milestones, then use the tools provided by the Github MCP server to remove them, or by running commands in behalf the user, such as the ones allowed by the tools provided by the Desktop Commander MCP server. If there is not tool that allows to delete the current repository's existing Labels and Milestones, then the Labels and Milestones needs to be removed in Github manually. Labels needs to be removed using GIT CLI commands (https://cli.github.com/manual/), and Milestones needs to be removed using GitHub's API through the Github CLI API command.

### 2. Define Labels and Milestones from PRD and tasks.
Reference the [config file](md: .ai_context_eng/context_config.md) to locate the project tasks files, the PRD file, and the tasks complexity report file.

*   Given the project tasks and the complexity report, a file called `labels-milestones.json` must be generated in the related subdirectory. Create the related subdirectory if does not exist.
*   This file content should describes the Labels and Milestones to use when register Issues in Github. This file content should be generated by mixing pre-defined Labels (described in a section above) with dynamic Labels and Milestones generated from what the PRD describes and the tasks nature.
*   The Milestones should be all derived dynamically from the PRD information(phases, stages, business_values, etc.). Milestones should be not repeated.
*   The dynamic Labels should be derived from the PRD information(phases, stages, business_values, etc.) and also from the tasks nature. Labels should be not repeated. Labels are categorized and each Label should have a color related to it category. 
*   Label categories should be used to indicate the "type", the "priority", the "status", the technical "component/area" of the issues, and also to indicate the "semver"(semantic versioning) and the technical "component/area" of a PR (Pull Request) as well.

### 2.1 Static Labels(for issues and PRs) to use when creates the `labels-milestones.json` file content
```json
{
    {
        "text": "type: feature",
        "color": "#1D76DB",
        "description": "New functionality (e.g., new cta, module, tool, prompt, or user capability)."
    },
    {
        "text": "type: enhancement",
        "color": "#84B6EB",
        "description": "Improvements to existing features."
    },
    {
        "text": "type: bug",
        "color": "#D73A4A",
        "description": "Defects or regressions."
    },
    {
        "text": "type: documentation",
        "color": "#0075CA",
        "description": "Docs, comments, or examples."
    },
    {
        "text": "type: chore",
        "color": "#BFDADC",
        "description": "Refactors, tests, or config."
    },
    {
        "text": "priority: high",
        "color": "#B60205",
        "description": "High priority task."
    },
    {
        "text": "priority: medium",
        "color": "#D4C5F9",
        "description": "Medium priority task."
    },
    {
        "text": "priority: low",
        "color": "#C2E0C6",
        "description": "Low priority task."
    },
    {
        "text": "status: pending",
        "color": "#ededed",
        "description": "Implementation haven't started yet."
    },
    {
        "text": "status: in progress",
        "color": "#FBCA04",
        "description": "Work is currently being done on this issue/task."
    },
    {
        "text": "status: needs review",
        "color": "#0E8A16",
        "description": "This issue/PR is ready for code review or documentation review."
    },
    {
        "text": "status: done",
        "color": "#0E8A16",
        "description": "This issue/PR is completed/merged."
    },
    {
        "text": "semver: PATCH",
        "color": "#ededed",
        "description": "PR that introduce backwards-compatible bug fixes."
    },
    {
        "text": "semver: MINOR",
        "color": "#FBCA04",
        "description": "PR that introduce functionality in a backwards-compatible manner."
    },
    {
        "text": "semver: MAJOR",
        "color": "#0E8A16",
        "description": "PR that introduce incompatible API changes."
    },
}
```

### 2.2 Illustrative Output Sample for Labels and Milestones generated dynamically from what the PRD describes and tasks nature.
```json
{
  "labels": [
    {
        "text": "component: db",
        "color": "#006B75",
        "description": "Database connection, schema, or query logic."
    },
    {
        "text": "component: mcp-server",
        "color": "#5319E7",
        "description": "MCP server core."
    },
    {
        "text": "component: prompts",
        "color": "#BFDADC",
        "description": "MCP prompts."
    },,
    {
        "text": "component: security",
        "color": "#B60205",
        "description": "Security, validation, or compliance."
    }
    ....
  ],
  "milestones": [
    {
        "title": "MVP: Local MCP Server",
        "state": "open",
        "description": "Deliver a working MCP server that runs locally, exposes MySQL schemas, and supports basic query execution. Includes: Project setup, DB connection, schema provider, query executor, MCP server, config, and main entry point."
    },
    {
        "title": "Security & Compliance",
        "state": "open",
        "description": "Ensure the server is secure for local use, with proper domain restriction, input validation, and sensitive data handling. Includes all security-related tasks."
    },
    {
        "title": "Developer Experience & Tooling",
        "state": "open",
        "description": "Improve maintainability, testing, and developer workflow. Includes logging, error handling, TypeScript interfaces, configuration, and deployment scripts."
    },
    {
        "title": "Documentation & Examples",
        "state": "open",
        "description": "Provide clear documentation, usage examples, and deployment instructions. Includes README, code comments, configuration docs, and example scripts."
    },
    {
        "title": "V1.0",
        "state": "open",
        "description": "First version of the product."
    }
    ...
  ]
}
```

### 2.3 Illustrative Output Sample when creates the `labels-milestones.json` file content by mixing dynamic labels with static labels, and dynamic milestones.

```json
{
  "labels": [
    {
        "text": "type: feature",
        "color": "#1D76DB",
        "description": "New functionality (e.g., new cta, module, tool, prompt, or user capability)."
    },
    {
        "text": "type: enhancement",
        "color": "#84B6EB",
        "description": "Improvements to existing features."
    },
    {
        "text": "type: bug",
        "color": "#D73A4A",
        "description": "Defects or regressions."
    },
    {
        "text": "type: documentation",
        "color": "#0075CA",
        "description": "Docs, comments, or examples."
    },
    ...
    {
        "text": "component: db",
        "color": "#006B75",
        "description": "Database connection, schema, or query logic."
    },
    {
        "text": "component: mcp-server",
        "color": "#5319E7",
        "description": "MCP server core."
    },
    {
        "text": "component: prompts",
        "color": "#BFDADC",
        "description": "MCP prompts."
    },,
    {
        "text": "component: security",
        "color": "#B60205",
        "description": "Security, validation, or compliance."
    }
    ....
  ],
  "milestones": [
    {
        "title": "MVP: Local MCP Server",
        "state": "open",
        "description": "Deliver a working MCP server that runs locally, exposes MySQL schemas, and supports basic query execution. Includes: Project setup, DB connection, schema provider, query executor, MCP server, config, and main entry point."
    },
    {
        "title": "Security & Compliance",
        "state": "open",
        "description": "Ensure the server is secure for local use, with proper domain restriction, input validation, and sensitive data handling. Includes all security-related tasks."
    },
    {
        "title": "Developer Experience & Tooling",
        "state": "open",
        "description": "Improve maintainability, testing, and developer workflow. Includes logging, error handling, TypeScript interfaces, configuration, and deployment scripts."
    },
    {
        "title": "Documentation & Examples",
        "state": "open",
        "description": "Provide clear documentation, usage examples, and deployment instructions. Includes README, code comments, configuration docs, and example scripts."
    },
    {
        "title": "V1.0",
        "state": "open",
        "description": "First version of the product."
    }
    ...
  ]
}
```

### 3. Submit Labels and Milestones to Github.

*   The Labels and Milestones generated in `labels-milestones.json` file must be submitted to the Github repository.
*   As a first option try to use the tools provided by the Github MCP server and Git MCP server. If there is not tool that allows to create/edit/delete Labels and Milestones, then the Labels and Milestones needs to be registered in Github manually. For manually labels/milestones creation, Labels needs to be created using GIT CLI commands (https://cli.github.com/manual/) and Milestones needs to be created using GitHub's API through the Github CLI API command. Below you have the details for each one.
*   Do not check for Labels/Milestones existence, just create them.

### 3.1 GIT CLI commands to register the Labels in Github.

- Generate and run the CLI commands to submit Labels from `labels-milestones.json` file to Github using tools provided by MCPs servers (such as the ones provided by Desktop Commander MCP server).
- If there is not tool for running the CLI commands in user behalf, then print the CLI commands and request to the user to open the GITHUB CLI terminal and run the command you provided.

Illustrative CLI command samples to generate and run:

```CLI
gh label create "type: feature" --color 1D76DB --description "New functionality (e.g., new module, tool, or prompt)"
gh label create "type: enhancement" --color 84B6EB --description "Improvements to existing features"
gh label create "type: bug" --color D73A4A --description "Defects or regressions"
gh label create "type: documentation" --color 0075CA --description "Docs, comments, or examples"
gh label create "type: refactor" --color FBCA04 --description "Codebase cleanup or restructuring"
gh label create "type: test" --color 5319E7 --description "Unit, integration, or system tests"
gh label create "type: config" --color BFDADC --description "Configuration or environment setup"
...

```

### 3.2 GIT CLI commands to register the Milestones in Github.

- Generate and run the CLI commands to submit Milestones from `labels-milestones.json` file to Github using tools provided by MCPs servers (such as the ones provided by Desktop Commander MCP server).
- If there is not tool for running the CLI commands in user behalf, then print the CLI commands and request to the user to open the GITHUB CLI terminal and run the command you provided.

Illustrative CLI command samples to generate and run:

```CLI
gh api --method POST /repos/<your_github_user>/<your_repo>/milestones -f title='MVP: Local MCP Server' -f state='open' -f description='Deliver a working MCP server that runs locally, exposes MySQL schemas, and supports basic query execution. Includes: Project setup, DB connection, schema provider, query executor, MCP server, config, and main entry point.'
...

```



