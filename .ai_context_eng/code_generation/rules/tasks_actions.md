---
description: Rules to apply when certain action is requested over a task.
---

# Task Actions

This guide contains the rules to apply when a certain action is requested for/over a task.

## Actions

1. **Convert Tasks into Github Issues**:

* User Input(chat message) to trigger the action: "generate github issues", or "ggis".
* Action to execute: Convert all tasks into Github Issues.
* Prompt to use: [`tasks_to_github_issues_converter.md`](/.ai_context_eng/code_generation/prompts/tasks_to_github_issues_converter.md).
