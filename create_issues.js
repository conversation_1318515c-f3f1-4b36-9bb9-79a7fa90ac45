const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

// Task to milestone mapping based on complexity report and PRD phases
const taskToMilestone = {
  1: 1,  // Phase 1.1: HTTP Server Foundation
  2: 1,  // Phase 1.1: HTTP Server Foundation (authentication is part of server foundation)
  3: 2,  // Phase 1.2: Advanced Node.js Features (storage system)
  4: 2,  // Phase 1.2: Advanced Node.js Features (multi-process architecture)
  5: 3,  // Phase 1.3: External API Integration (Stripe)
  6: 3,  // Phase 1.3: External API Integration (Mailgun)
  7: 4,  // Phase 1.4: Client Applications (CLI)
  8: 4,  // Phase 1.4: Client Applications (Web)
  9: 5,  // Phase 2.1: Domain-Driven Design
  10: 7, // Phase 2.3: TypeScript Migration
  11: 8, // Phase 3.1: Next.js Implementation
  12: 11 // Phase 3.4: SvelteKit & Framework Comparison
};

// Component mapping based on task content
const taskToComponents = {
  1: ['component: server'],
  2: ['component: authentication', 'component: server'],
  3: ['component: storage'],
  4: ['component: server'],
  5: ['component: payment'],
  6: ['component: email'],
  7: ['component: cli'],
  8: ['component: web'],
  9: ['component: architecture'],
  10: ['component: typescript', 'component: architecture'],
  11: ['component: nextjs', 'component: web'],
  12: ['component: sveltekit', 'component: web']
};

// Phase mapping
const taskToPhase = {
  1: 'phase: foundation', 2: 'phase: foundation', 3: 'phase: foundation', 4: 'phase: foundation',
  5: 'phase: foundation', 6: 'phase: foundation', 7: 'phase: foundation', 8: 'phase: foundation',
  9: 'phase: architecture', 10: 'phase: architecture',
  11: 'phase: frameworks', 12: 'phase: frameworks'
};

async function parseTaskFile(filePath) {
  const content = await fs.readFile(filePath, 'utf8');
  const lines = content.split('\n');
  
  const task = {};
  let currentSection = '';
  let sectionContent = [];
  
  for (const line of lines) {
    if (line.startsWith('# Task ID:')) {
      task.id = parseInt(line.split(':')[1].trim());
    } else if (line.startsWith('# Title:')) {
      task.title = line.split(':')[1].trim();
    } else if (line.startsWith('# Status:')) {
      task.status = line.split(':')[1].trim();
    } else if (line.startsWith('# Dependencies:')) {
      task.dependencies = line.split(':')[1].trim();
    } else if (line.startsWith('# Priority:')) {
      task.priority = line.split(':')[1].trim();
    } else if (line.startsWith('# Description:')) {
      task.description = line.split(':')[1].trim();
    } else if (line.startsWith('# Details:')) {
      currentSection = 'details';
      sectionContent = [];
    } else if (line.startsWith('# Test Strategy:')) {
      if (currentSection === 'details') {
        task.details = sectionContent.join('\n').trim();
      }
      currentSection = 'testStrategy';
      sectionContent = [];
    } else if (line.startsWith('# Subtasks:')) {
      if (currentSection === 'testStrategy') {
        task.testStrategy = sectionContent.join('\n').trim();
      }
      currentSection = 'subtasks';
      sectionContent = [];
    } else if (currentSection) {
      sectionContent.push(line);
    }
  }
  
  if (currentSection === 'subtasks') {
    task.subtasks = sectionContent.join('\n').trim();
  }
  
  return task;
}

function formatSubtasks(subtasksText) {
  if (!subtasksText) return '';
  
  const lines = subtasksText.split('\n');
  const formatted = [];
  
  for (const line of lines) {
    if (line.startsWith('## ')) {
      // Main subtask
      const title = line.replace('## ', '').replace(' [pending]', '');
      formatted.push(`- [ ] ${title}`);
    }
  }
  
  return formatted.join('\n');
}

function createIssueBody(task) {
  const subtasks = formatSubtasks(task.subtasks);
  
  return `## Description
${task.description}

${subtasks ? `## Subtasks
${subtasks}

` : ''}## Acceptance Criteria
${task.details ? task.details.split('.').slice(0, 3).map(item => `- ${item.trim()}`).filter(item => item.length > 2).join('\n') : '- Implementation meets all technical requirements\n- Code follows project standards\n- Tests pass successfully'}

## Technical Requirements
- **Dependencies**: ${task.dependencies === 'None' ? 'None' : task.dependencies}
- **Priority**: ${task.priority}
- **Architecture Impact**: Core system component
- **Performance Considerations**: Must meet system performance requirements

## Implementation Notes
${task.details ? task.details.substring(0, 300) + '...' : 'Detailed implementation notes available in task documentation.'}

## Testing Strategy
${task.testStrategy || 'Comprehensive testing including unit tests, integration tests, and performance validation.'}

## Definition of Done
- [ ] Code implemented and reviewed
- [ ] Tests written and passing
- [ ] Documentation updated
- [ ] Performance requirements met
- [ ] Integration testing completed`;
}

function getLabels(task) {
  const labels = ['type: feature', `priority: ${task.priority}`, 'status: pending'];
  
  // Add component labels
  if (taskToComponents[task.id]) {
    labels.push(...taskToComponents[task.id]);
  }
  
  // Add phase label
  if (taskToPhase[task.id]) {
    labels.push(taskToPhase[task.id]);
  }
  
  return labels;
}

async function createGitHubIssue(task) {
  const title = `${task.id}: ${task.title}`;
  const body = createIssueBody(task);
  const labels = getLabels(task);
  const milestone = taskToMilestone[task.id];
  
  console.log(`Creating issue for Task ${task.id}: ${task.title}`);
  
  // Create labels string for CLI
  const labelsStr = labels.map(l => `"${l}"`).join(' ');
  
  try {
    const command = `gh issue create --title "${title}" --body "${body.replace(/"/g, '\\"')}" --label ${labelsStr} --milestone ${milestone}`;
    const result = execSync(command, { encoding: 'utf8', cwd: process.cwd() });
    console.log(`✓ Created issue ${task.id}: ${result.trim()}`);
    return result.trim();
  } catch (error) {
    console.error(`✗ Failed to create issue ${task.id}:`, error.message);
    return null;
  }
}

async function main() {
  const tasksDir = '.ai_context_eng/project_management/tasks';
  const files = await fs.readdir(tasksDir);
  const taskFiles = files.filter(f => f.startsWith('task_') && f.endsWith('.txt')).sort();
  
  console.log(`Found ${taskFiles.length} task files`);
  
  const results = [];
  
  // Skip task 1 since it's already created
  for (const file of taskFiles.slice(1)) {
    const filePath = path.join(tasksDir, file);
    const task = await parseTaskFile(filePath);
    
    if (task.id && task.title) {
      const result = await createGitHubIssue(task);
      results.push({ taskId: task.id, result });
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log('\n=== Summary ===');
  results.forEach(({ taskId, result }) => {
    console.log(`Task ${taskId}: ${result ? '✓ Created' : '✗ Failed'}`);
  });
}

main().catch(console.error);