const fs = require('fs').promises;
const path = require('path');

async function updateTaskFile(filePath, taskId, issueNumber) {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    
    // Check if github_issue already exists
    if (content.includes('# github_issue:')) {
      console.log(`Task ${taskId} already has github_issue reference`);
      return;
    }
    
    // Find the description line and add github_issue after it
    const lines = content.split('\n');
    let descriptionIndex = -1;
    
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('# Description:')) {
        descriptionIndex = i;
        break;
      }
    }
    
    if (descriptionIndex === -1) {
      console.error(`Could not find description line in task ${taskId}`);
      return;
    }
    
    // Insert github_issue line after description
    lines.splice(descriptionIndex + 1, 0, `# github_issue: ${issueNumber}`);
    
    const updatedContent = lines.join('\n');
    await fs.writeFile(filePath, updatedContent, 'utf8');
    
    console.log(`✓ Updated task ${taskId} with GitHub issue ${issueNumber}`);
  } catch (error) {
    console.error(`✗ Failed to update task ${taskId}:`, error.message);
  }
}

async function main() {
  const tasksDir = '.ai_context_eng/project_management/tasks';
  
  // Task ID to Issue Number mapping
  const taskToIssue = {
    3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, 10: 10, 11: 11, 12: 12
  };
  
  console.log('Updating task files with GitHub issue references...');
  
  for (const [taskId, issueNumber] of Object.entries(taskToIssue)) {
    const filePath = path.join(tasksDir, `task_${taskId.padStart(3, '0')}.txt`);
    await updateTaskFile(filePath, taskId, issueNumber);
  }
  
  console.log('\n✅ All task files updated successfully!');
}

main().catch(console.error);