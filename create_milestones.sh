#!/bin/bash

# Create remaining milestones
gh api --method POST /repos/enbi-dev/pizza_delivery_service/milestones -f title='Phase 1.3: External API Integration' -f state='open' -f description='Complete Stripe payment integration and Mailgun email notifications via raw HTTPS requests without SDK dependencies. Covers Day 5 deliverables.'

gh api --method POST /repos/enbi-dev/pizza_delivery_service/milestones -f title='Phase 1.4: Client Applications' -f state='open' -f description='Build CLI client and web application with vanilla JavaScript, implementing state management and cross-client session handling. Covers Day 6 deliverables.'

gh api --method POST /repos/enbi-dev/pizza_delivery_service/milestones -f title='Phase 2.1: Domain-Driven Design' -f state='open' -f description='Transform vanilla implementation into domain-driven design with bounded contexts, aggregates, domain events, and business rules enforcement. Covers Day 7 deliverables.'

gh api --method POST /repos/enbi-dev/pizza_delivery_service/milestones -f title='Phase 2.2: Clean Architecture' -f state='open' -f description='Implement Clean Architecture with layered structure, dependency inversion, use cases, and repository patterns. Covers Day 8 deliverables.'

gh api --method POST /repos/enbi-dev/pizza_delivery_service/milestones -f title='Phase 2.3: TypeScript Migration' -f state='open' -f description='Complete TypeScript migration with advanced type modeling, type-safe dependency injection, and compile-time domain rule enforcement. Covers Day 9 deliverables.'

gh api --method POST /repos/enbi-dev/pizza_delivery_service/milestones -f title='Phase 3.1: Next.js Implementation' -f state='open' -f description='Build production-ready Next.js application maintaining Clean Architecture boundaries with Server Components and advanced data fetching. Covers Day 10 deliverables.'

gh api --method POST /repos/enbi-dev/pizza_delivery_service/milestones -f title='Phase 3.2: Performance Optimization' -f state='open' -f description='Optimize build pipeline, implement performance monitoring, and achieve measurable performance improvements. Covers Day 11 deliverables.'

gh api --method POST /repos/enbi-dev/pizza_delivery_service/milestones -f title='Phase 3.3: Production Deployment' -f state='open' -f description='Deploy production system with comprehensive documentation, monitoring, and maintenance procedures. Covers Day 12 deliverables.'

gh api --method POST /repos/enbi-dev/pizza_delivery_service/milestones -f title='Phase 3.4: SvelteKit & Framework Comparison' -f state='open' -f description='Implement SvelteKit application with Clean Architecture and conduct comprehensive framework comparison analysis. Covers Day 13 deliverables.'

echo "All milestones created successfully!"